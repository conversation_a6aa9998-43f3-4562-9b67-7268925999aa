# Customization Guide for Athar Commons

This guide provides instructions on how to customize the Athar Commons gem for your specific service needs.

## Table of Contents

1. [Customizing the Base Controller](#customizing-the-base-controller)
2. [Customizing Serialization](#customizing-serialization)
3. [Customizing Filtering and Sorting](#customizing-filtering-and-sorting)
4. [Customizing Pagination](#customizing-pagination)
5. [Customizing API Documentation](#customizing-api-documentation)

## Customizing the Base Controller

The base controller is the foundation of your API. You can customize it to add service-specific functionality.

### Adding Authentication

```ruby
module Api
  class BaseController < ActionController::API
    include Athar::Commons::Api::ParameterTypeConverter
    include Athar::Commons::Api::FilterableSortable
    include Athar::Commons::Api::Paginatable
    include Athar::Commons::Api::Serializable
    include Athar::Commons::Api::JsonapiDocs

    before_action :convert_param_types
    before_action :authenticate_user!

    # Authentication logic
    def current_user
      @current_user ||= begin
        token = request.headers['Authorization']&.split(' ')&.last
        User.find_by_token(token) if token.present?
      end
    end

    def authenticate_user!
      unless current_user
        serialize_errors({ detail: "Authentication required" }, :unauthorized)
      end
    end
  end
end
```

### Adding Authorization

```ruby
module Api
  class BaseController < ActionController::API
    # Include concerns...

    # Authorization helper
    def authorize!(action, subject)
      unless can?(action, subject)
        serialize_errors({ detail: "You are not authorized to perform this action" }, :forbidden)
        return false
      end
      true
    end

    def can?(action, subject)
      # Your authorization logic here
      true
    end
  end
end
```

### Adding Custom Error Handling

```ruby
module Api
  class BaseController < ActionController::API
    # Include concerns...

    # Custom error handling
    rescue_from CustomError::InvalidOperation do |e|
      serialize_errors({ detail: e.message, code: e.error_code }, :unprocessable_entity)
    end
  end
end
```

## Customizing Serialization

You can customize how resources are serialized by overriding methods in the `Serializable` concern.

### Adding Custom Metadata

```ruby
def prepare_serialization_options(resource, options = {})
  default_options = super
  
  # Add service-specific metadata
  default_options[:meta][:service_name] = "your-service"
  default_options[:meta][:version] = "1.0.0"
  
  default_options
end
```

### Customizing Error Responses

```ruby
def serialize_errors(errors, status = :unprocessable_entity)
  # Add service-specific error metadata
  meta = {
    error_code: "YOUR_SERVICE_ERROR",
    service: "your-service"
  }
  
  render jsonapi_errors: errors, status: status, meta: meta
end
```

## Customizing Filtering and Sorting

You can customize how filtering and sorting are applied by overriding methods in the `FilterableSortable` concern.

### Custom Search Implementation

```ruby
def apply_search_filter(collection)
  search_query = params.dig(:filter, :search)
  return collection unless search_query.present?
  
  # Service-specific search implementation
  collection.where(
    "name ILIKE :query OR description ILIKE :query",
    query: "%#{search_query}%"
  )
end
```

### Restricting Allowed Filters

```ruby
def get_allowed_filters(model_class)
  # Define a specific list of allowed filters for this controller
  %w[name status created_at department_id]
end
```

### Adding Custom Filters

```ruby
def apply_filters(collection)
  # Apply custom filters before standard filters
  if params.dig(:filter, :active_only) == "true"
    collection = collection.where(active: true)
  end
  
  # Call the parent method to apply standard filters
  super(collection)
end
```

## Customizing Pagination

You can customize how pagination is applied by overriding methods in the `Paginatable` concern.

### Changing Pagination Defaults

```ruby
def get_pagination_options
  { jsonapi: true, items: 50, max_items: 200 }
end
```

### Conditional Pagination

```ruby
def index
  @collection = YourModel.all
  
  # Apply filters
  apply_filters(@collection) do |filtered_collection|
    # Conditionally apply pagination
    if params[:paginate] == "false"
      # Skip pagination
      serialize_response(filtered_collection)
    else
      # Apply pagination
      records, meta = paginate(filtered_collection)
      serialize_response(records, meta: meta)
    end
  end
end
```

## Customizing API Documentation

You can customize the API documentation by overriding methods in the `JsonapiDocs` concern.

### Adding Custom Parameter Groups

```ruby
def self.custom_param_groups
  # Call the parent method to get the standard parameter groups
  jsonapi_param_groups
  
  # Add your own parameter groups
  def_param_group :your_service_params do
    param :custom_param, String, desc: "A custom parameter specific to your service"
    param :another_param, Integer, desc: "Another custom parameter"
  end
end

# Initialize the custom parameter groups
custom_param_groups
```

### Customizing Documentation Content

```ruby
def self.common_jsonapi_docs
  <<-HTML
  <b>Your Service API</b>
  
  This API provides access to your service's resources.
  
  <b>Authentication</b>
  
  All endpoints require authentication using a Bearer token.
  
  #{super}
  HTML
end
```
