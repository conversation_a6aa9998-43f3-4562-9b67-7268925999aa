#!/usr/bin/env ruby

require 'rspec'
require 'active_model'
require 'active_support'
require 'active_support/core_ext'
require 'securerandom'

# Load our ActiveStruct files directly
require_relative 'lib/athar/commons/active_struct/associations'
require_relative 'lib/athar/commons/active_struct/base'
require_relative 'lib/athar/commons/active_struct/collection'

RSpec.describe Athar::Commons::ActiveStruct::Base do
  describe 'ID attribute configuration' do
    context 'with default configuration' do
      let(:model_class) do
        Class.new(described_class) do
          attribute :name, :string
        end
      end

      it 'has ID attribute enabled by default' do
        expect(model_class.has_id_attribute).to be true
        expect(model_class.auto_generate_id).to be true
        expect(model_class.id_enabled?).to be true
      end

      it 'creates instances with auto-generated IDs' do
        instance = model_class.new(name: 'Test')
        expect(instance).to respond_to(:id)
        expect(instance.id).to be_present
        expect(instance.persisted?).to be true
      end

      it 'supports manual ID setting' do
        instance = model_class.new(id: 'custom-123', name: 'Test')
        expect(instance.id).to eq('custom-123')
        expect(instance.persisted?).to be true
      end
    end

    context 'with without_id configuration' do
      let(:model_class) do
        Class.new(described_class) do
          without_id
          attribute :name, :string
        end
      end

      it 'disables ID attribute completely' do
        expect(model_class.has_id_attribute).to be false
        expect(model_class.auto_generate_id).to be false
        expect(model_class.id_enabled?).to be false
      end

      it 'creates instances without ID attribute' do
        instance = model_class.new(name: 'Test')
        expect(instance).not_to respond_to(:id)
        expect(instance.persisted?).to be false
      end

      it 'ignores ID in initialization' do
        instance = model_class.new(id: 'ignored', name: 'Test')
        expect(instance).not_to respond_to(:id)
        expect(instance.persisted?).to be false
      end
    end

    context 'with manual ID configuration' do
      let(:model_class) do
        Class.new(described_class) do
          with_id(auto_generate: false)
          attribute :name, :string
        end
      end

      it 'has ID attribute but no auto-generation' do
        expect(model_class.has_id_attribute).to be true
        expect(model_class.auto_generate_id).to be false
        expect(model_class.id_enabled?).to be true
      end

      it 'creates instances without auto-generated IDs' do
        instance = model_class.new(name: 'Test')
        expect(instance).to respond_to(:id)
        expect(instance.id).to be_blank
        expect(instance.persisted?).to be false
      end

      it 'accepts manual ID setting' do
        instance = model_class.new(id: 'manual-123', name: 'Test')
        expect(instance.id).to eq('manual-123')
        expect(instance.persisted?).to be true
      end
    end
  end

  describe 'form builder support methods' do
    context 'with ID enabled' do
      let(:model_class) do
        Class.new(described_class) do
          attribute :name, :string
        end
      end

      it 'provides correct form builder methods when persisted' do
        instance = model_class.new(name: 'Test')
        expect(instance.to_key).to eq([instance.id])
        expect(instance.to_param).to eq(instance.id)
        expect(instance.new_record?).to be false
      end

      it 'provides correct form builder methods when not persisted' do
        instance = model_class.new(id: nil, name: 'Test')
        expect(instance.to_key).to be_nil
        expect(instance.to_param).to be_nil
        expect(instance.new_record?).to be true
      end
    end

    context 'with ID disabled' do
      let(:model_class) do
        Class.new(described_class) do
          without_id
          attribute :name, :string
        end
      end

      it 'provides correct form builder methods' do
        instance = model_class.new(name: 'Test')
        expect(instance.to_key).to be_nil
        expect(instance.to_param).to be_nil
        expect(instance.persisted?).to be false
        expect(instance.new_record?).to be true
      end
    end
  end
end

RSpec.describe Athar::Commons::ActiveStruct::Collection do
  let(:id_enabled_model_class) do
    Class.new(Athar::Commons::ActiveStruct::Base) do
      attribute :name, :string
    end
  end

  let(:id_disabled_model_class) do
    Class.new(Athar::Commons::ActiveStruct::Base) do
      without_id
      attribute :name, :string
    end
  end

  let(:id_enabled_collection_class) do
    model_class = id_enabled_model_class
    Class.new do
      include Athar::Commons::ActiveStruct::Collection
      define_method(:item_class) { model_class }
    end
  end

  let(:id_disabled_collection_class) do
    model_class = id_disabled_model_class
    Class.new do
      include Athar::Commons::ActiveStruct::Collection
      define_method(:item_class) { model_class }
    end
  end

  describe 'with ID-enabled models' do
    let(:collection) { id_enabled_collection_class.new }

    before do
      collection << id_enabled_model_class.new(name: 'Item 1')
      collection << id_enabled_model_class.new(name: 'Item 2')
    end

    it 'provides ID management functionality' do
      expect(collection.size).to eq(2)
      expect(collection.ids.size).to eq(2)
      expect(collection.ids.all?(&:present?)).to be true
    end

    it 'supports finding by ID' do
      first_id = collection.ids.first
      found_item = collection.find(first_id)
      expect(found_item).not_to be_nil
      expect(found_item.id).to eq(first_id)
    end

    it 'supports setting IDs' do
      new_ids = ['id1', 'id2']
      collection.ids = new_ids
      expect(collection.size).to eq(2)
      expect(collection.ids).to eq(new_ids)
    end
  end

  describe 'with ID-disabled models' do
    let(:collection) { id_disabled_collection_class.new }

    before do
      collection << id_disabled_model_class.new(name: 'Item 1')
      collection << id_disabled_model_class.new(name: 'Item 2')
    end

    it 'handles models without IDs gracefully' do
      expect(collection.size).to eq(2)
      expect(collection.ids).to be_empty
    end

    it 'returns nil when finding by ID' do
      found_item = collection.find('any-id')
      expect(found_item).to be_nil
    end

    it 'ignores ID setting attempts' do
      original_size = collection.size
      collection.ids = ['id1', 'id2']
      expect(collection.size).to eq(original_size)
      expect(collection.ids).to be_empty
    end
  end
end

# Run the tests
RSpec::Core::Runner.run([__FILE__], $stderr, $stdout)
