# frozen_string_literal: true
require_relative "lib/athar/commons/version"

# noinspection RubyArgCount
Gem::Specification.new do |spec|
  spec.name = "athar_commons"
  spec.version = Athar::Commons::VERSION
  spec.authors = [ "Athar Team" ]
  spec.email = [ "<EMAIL>" ]

  spec.summary = "Common functionality for Athar services"
  spec.description = "Shared controllers, concerns, and utilities for Athar microservices"
  spec.homepage = "https://github.com/athar/athar-commons"
  spec.license = "MIT"

  spec.required_ruby_version = ">= 3.0.0"

  spec.metadata["homepage_uri"] = spec.homepage
  spec.metadata["source_code_uri"] = "https://github.com/athar/athar-commons"
  spec.metadata["changelog_uri"] = "https://github.com/athar/athar-commons/blob/main/CHANGELOG.md"

  # Specify which files should be added to the gem when it is released.
  # The `git ls-files -z` loads the files in the RubyGem that have been added into git.
  spec.files = Dir.glob("{app,lib}/**/*") + %w[MIT-LICENSE Rakefile README.md CHANGELOG.md]

  spec.require_paths = [ "lib" ]

  # Dependencies
  spec.add_dependency "rails", ">= 7.0"
  spec.add_dependency "jsonapi-serializer"
  spec.add_dependency "apipie-rails"
  spec.add_dependency "jsonapi.rb"
  spec.add_dependency "ransack"
  spec.add_dependency "maruku"
  spec.add_dependency "pagy"
  spec.add_dependency "rexml"

  # Athar Dependencies
  spec.add_dependency "athar_rpc"

  # Development dependencies
  spec.add_development_dependency "rspec"
  spec.add_development_dependency "rspec-rails"
  spec.add_development_dependency "factory_bot_rails"
  spec.add_development_dependency "ostruct"
  spec.add_development_dependency "sqlite3"
end
