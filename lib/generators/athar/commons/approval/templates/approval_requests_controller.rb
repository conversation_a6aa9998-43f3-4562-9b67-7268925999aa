module Api
  class ApprovalRequestsController < ApplicationController
    before_action :authenticate_session!
    before_action :set_approval_request, only: %i[show approve reject cancel]
    before_action :authorize_pending_approvals, only: %i[pending_approvals]
    before_action :authorize_read_all_or_own, only: %i[index]
    before_action :authorize_read_specific, only: %i[show]
    before_action :authorize_approve, only: %i[approve]
    before_action :authorize_reject, only: %i[reject]
    before_action :authorize_cancel, only: %i[cancel]

    api! "Lists all approval requests created by the current user"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param_group :pagination_params
    param_group :filter_params
    param_group :sort_params
    param_group :include_params
    param_group :fields_params
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Returns a list of approval requests based on the user's permissions:
      — Users with <code>:read, :approval_request</code> permission can see all approval requests
      — Users with <code>:read_own, :approval_request</code> permission can only see their own approval requests

      Supports filtering, sorting, and pagination.
    HTML
    )
    returns code: 200, desc: "List of approval requests"

    def index
      apply_filters(@collection) do |filtered_and_sorted|
        records, meta = paginate(filtered_and_sorted)
        serialize_response(records, meta: meta)
      end
    end

    api! "Retrieves a specific approval request"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the approval request"
    param_group :include_params
    param_group :fields_params
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Fetches detailed information about a specific approval request by ID.
      — Users with <code>:read, :approval_request</code> permission can see any approval request
      — Users with <code>:read_own, :approval_request</code> permission can only see their own approval requests
    HTML
    )
    returns code: 200, desc: "Approval request details"

    def show
      serialize_response(@approval_request)
    end

    api! "Approves an approval request"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the approval request"
    param :comment, String, desc: "Optional comment for the approval"
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Approves an approval request.
      The current user must be an approver for the current step of the request.
      Requires permission: <code>:approve, :approval_request</code>.
    HTML
    )
    returns code: 200, desc: "Approval request approved successfully"
    error code: 422, desc: "Validation errors"

    def approve
      process_approval_action(:approve!, params[:comment])
    end

    api! "Rejects an approval request"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the approval request"
    param :comment, String, desc: "Optional comment for the rejection"
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Rejects an approval request.
      The current user must be an approver for the current step of the request.
      Requires permission: <code>:reject, :approval_request</code>.
    HTML
    )
    returns code: 200, desc: "Approval request rejected successfully"
    error code: 422, desc: "Validation errors"

    def reject
      process_approval_action(:reject!, params[:comment])
    end

    api! "Cancels an approval request"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the approval request"
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Cancels an approval request.
      Only the requestor can cancel their own request.
      Requires permission: <code>:cancel, :approval_request</code> or <code>:cancel_own, :approval_request</code>.
    HTML
    )
    returns code: 200, desc: "Approval request canceled successfully"
    error code: 422, desc: "Validation errors"

    def cancel
      process_approval_action(:cancel!)
    end

    api! "Lists all approval requests that the current user can approve"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param_group :pagination_params
    param_group :filter_params
    param_group :sort_params
    param_group :include_params
    param_group :fields_params
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Returns a list of all approval requests that the current user can approve.
      These are requests where the current user is an approver for the current step.
      Supports filtering, sorting, and pagination.
      Requires permission: <code>:read, :approval_request</code> or <code>:approve, :approval_request</code>.
    HTML
    )
    returns code: 200, desc: "List of approval requests"

    def pending_approvals
      service = Athar::Commons::Services::ApprovalService.new
      collection = service.approvable_by(current_user.id)

      records, meta = paginate(collection)
      serialize_response(records, meta: meta)
    end

    private

    def process_approval_action(action, comment = nil)
      args = [current_user.id]
      args << comment if comment.present? || action == :approve! || action == :reject!

      result = @approval_request.public_send(action, *args)

      if result.success?
        serialize_response(@approval_request.reload)
      else
        serialize_errors({ detail: result.message, errors: result.errors })
      end
    end

    def set_approval_request
      @approval_request = ApprovalRequest.find(params[:id])
    rescue ActiveRecord::RecordNotFound
      serialize_errors({ detail: "Approval request not found" }, :not_found)
    end

    def authorize_read_all_or_own
      if can?(:read, :approval_request)
        @collection = ApprovalRequest.all
      elsif can?(:read_own, :approval_request)
        @collection = ApprovalRequest.where(requestor_id: current_user.id)
      else
        render_forbidden("You don't have permission to view approval requests")
        false
      end
    end

    def authorize_read_specific
      unless can?(:read, :approval_request) ||
             (can?(:read_own, :approval_request) && is_own_request?)
        render_forbidden("You don't have permission to view this approval request")
        false
      end
    end

    def authorize_approve
      authorize!(:approve, :approval_request)
    end

    def authorize_reject
      authorize!(:reject, :approval_request)
    end

    def authorize_cancel
      if (can?(:cancel, :approval_request) || can?(:cancel_own, :approval_request)) && is_own_request?
        true
      else
        render_forbidden("You don't have permission to cancel this approval request")
        false
      end
    end

    def authorize_pending_approvals
      unless can?(:read, :approval_request) || can?(:approve, :approval_request)
        render_forbidden("You don't have permission to view pending approvals")
        false
      end
    end

    def is_own_request?
      @approval_request.requestor_id == current_user.id
    end
  end
end
