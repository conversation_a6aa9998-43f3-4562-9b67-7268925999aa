# frozen_string_literal: true

# Require the dependencies explicitly
require "forwardable"         # Required by apipie-rails
require "rails"
require "jsonapi/serializer"  # For serializing models to JSON:API format
require "jsonapi"             # For JSON:API filtering, sorting, etc.
require "apipie-rails"
require "ransack"
require "maruku"
require "pagy"
require "pagy/extras/metadata"
require "pagy/extras/jsonapi"
require "pagy/extras/limit"

module Athar
  module Commons
    class Error < StandardError; end

    # Require all files in the gem
    require_relative "commons/version"
    require_relative "commons/engine"
    require_relative "commons/apipie/DSL"
    require_relative "commons/apipie/validators"
    require_relative "commons/api/errors"

    # Require models and concerns
    require_relative "commons/models/concerns/approvable"

    # Require ActiveStruct framework
    require_relative "commons/active_struct"

    # Require service clients
    require_relative "commons/services/approval_client"
    require_relative "commons/services/approval_service"
    # require_relative "commons/services/mock_approval_client"
  end
end
