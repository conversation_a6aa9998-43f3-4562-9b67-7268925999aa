# frozen_string_literal: true

# Require the dependencies explicitly
require "forwardable"         # Required by apipie-rails
require "rails"
require "jsonapi/serializer"  # For serializing models to JSON:API format
require "jsonapi"             # For JSON:API filtering, sorting, etc.
require "apipie-rails"
require "ransack"
require "maruku"
require "pagy"
require "pagy/extras/metadata"
require "pagy/extras/jsonapi"
require "pagy/extras/limit"

module Athar
  module Commons
    class Error < StandardError; end

    # Require all files in the gem
    require_relative "commons/version"
    require_relative "commons/engine"
    require_relative "commons/apipie/DSL"
    require_relative "commons/apipie/validators"
    require_relative "commons/api/errors"

    # Require models and concerns
    require_relative "commons/models/concerns/approvable"

    # Require ActiveStruct framework
    require_relative "commons/active_struct"

    # Manually require controller concerns for testing
    # (These are normally autoloaded by Rails)
    require_relative "../../app/controllers/concerns/athar/commons/api/concerns/parameter_type_converter"
    require_relative "../../app/controllers/concerns/athar/commons/api/concerns/paginatable"
    require_relative "../../app/controllers/concerns/athar/commons/api/concerns/serializable"
    require_relative "../../app/controllers/concerns/athar/commons/api/concerns/filterable_sortable"
    require_relative "../../app/controllers/concerns/athar/commons/api/concerns/jsonapi_docs"

    # Require model concerns
    require_relative "../../app/models/concerns/athar/commons/models/concerns/ransackable"
    require_relative "../../app/models/concerns/athar/commons/models/concerns/acts_as_approvable"

    # Require service clients
    require_relative "commons/services/approval_client"
    require_relative "commons/services/approval_service"
    # require_relative "commons/services/mock_approval_client"
  end
end
