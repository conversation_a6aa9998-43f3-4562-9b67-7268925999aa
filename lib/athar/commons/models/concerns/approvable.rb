module Athar
  module Commons
    module Models
      module Concerns
        module Approvable
          extend ActiveSupport::Concern

          # Define instance methods in a separate module that can be included by acts_as_approvable
          module InstanceMethods
            # Submit for approval
            # @param user [User, String, Integer] The user or user ID requesting approval
            # @param project [Project, String, Integer, nil] The project or project ID (if applicable)
            # @param context [Hash] Additional context for the approval workflow
            # @return [<PERSON><PERSON><PERSON>] Whether the request was created successfully
            def submit_for_approval(user, project = nil, context = {})
              return false if approval_request.present?

              # Extract IDs from objects if needed
              user_id = user.respond_to?(:id) ? user.id : user
              project_id = project.respond_to?(:id) ? project.id : project

              begin
                # Fetch the workflow from Core using the RPC client
                client = Athar::Commons::Services::ApprovalClient.new
                sequence = client.generate_sequence(
                  approval_action,
                  self.class.name,
                  system_name,
                  user_id,
                  project_id,
                  context
                )

                # If sequence is nil, the Core service might be unavailable
                unless sequence
                  Rails.logger.error "Failed to generate approval sequence for #{self.class.name} ##{id}"
                  return false
                end
                
                # Log the successful sequence generation
                Rails.logger.info "Generated approval sequence for #{self.class.name} ##{id} with workflow: #{sequence.workflow_name}"

                # Check if the workflow is auto-approved
                if sequence.respond_to?(:auto_approved) && sequence.auto_approved
                  # Get the auto-approval reason if available
                  auto_approval_reason = sequence.respond_to?(:auto_approval_reason) ? sequence.auto_approval_reason : "Auto-approved by workflow"
                  
                  # Log the auto-approval
                  Rails.logger.info "Auto-approving #{self.class.name} ##{id}: #{auto_approval_reason}"
                  
                  # Create an auto-approved request
                  attrs = {
                    workflow_id: sequence.workflow_id,
                    workflow_name: sequence.workflow_name,
                    requestor_id: user_id,
                    status: :approved,
                    steps_data: [],
                    comments: [{ user_id: 'system', comment: auto_approval_reason, created_at: Time.current }]
                  }

                  # Add project_id if applicable
                  attrs[:project_id] = project_id if project_id

                  # Create the approval request
                  create_approval_request!(attrs)

                  # Call the status change callback
                  on_approval_status_change('approved', nil)

                  return true
                end

                # Convert the steps to a format that can be stored in the database
                steps_data = sequence.steps.map do |step|
                  step_data = {
                    'step_id' => step.step_id,
                    'name' => step.name,
                    'sequence' => step.sequence,
                    'approval_type' => step.approval_type,
                    'approver_ids' => step.approver_ids.to_a
                  }
                  
                  # Add selection explanation if available
                  if step.respond_to?(:selection_explanation) && step.selection_explanation.present?
                    step_data['selection_explanation'] = step.selection_explanation
                  end
                  
                  step_data
                end

                # Create the approval request with steps
                attrs = {
                  workflow_id: sequence.workflow_id,
                  workflow_name: sequence.workflow_name,
                  requestor_id: user_id,
                  status: :pending,
                  steps_data: steps_data
                }

                # Add project_id if applicable
                attrs[:project_id] = project_id if project_id

                # Create the approval request
                create_approval_request!(attrs)

                true
              rescue StandardError => e
                Rails.logger.error "Error submitting for approval: #{e.message}"
                Rails.logger.error e.backtrace.join("\n") if e.backtrace
                
                # Add more specific error handling
                error_message = case e.message
                                when /No approval workflow found/
                                  "No approval workflow found for #{approval_action}"
                                when /Missing required context/
                                  "Missing required context for approval workflow. Please provide: #{context.keys.join(', ')}"
                                when /not applicable/
                                  "The approval workflow is not applicable with the provided context"
                                else
                                  "Failed to submit for approval: #{e.message}"
                                end
                
                # Store the error for the UI to display
                @approval_error = error_message
                
                false
              end
            end
            
            # Get the last approval error
            def approval_error
              @approval_error
            end

            # Handle missing approval request
            def handle_missing_approval_request(method_name, *args, raise_error: false, **kwargs)
              result = Athar::Commons::Services::ApprovalResult.failure("No approval request found")
              raise StandardError, result.message if raise_error
              result
            end

            # Default implementation of the status change callback
            # @param new_status [String] The new status of the approval request
            # @param previous_status [String] The previous status of the approval request
            # TODO: maybe this is not needed
            def on_approval_status_change(new_status, previous_status)
              # Default implementation - log the status change
              Rails.logger.info "Approval status changed from #{previous_status} to #{new_status} for #{self.class.name} ##{id}"

              # Models should override this method to handle status changes
            end

            # Check if this record is approved in the approval workflow
            def approval_approved?
              approval_request&.approved?
            end

            # Check if this record is rejected in the approval workflow
            def approval_rejected?
              approval_request&.rejected?
            end

            # Check if this record is pending approval in the approval workflow
            def approval_pending?
              approval_request&.pending?
            end

            # Get the current approval step
            def current_approval_step
              approval_request&.current_step
            end

            # Get all potential approvers for the current step
            def current_approvers
              step = current_approval_step
              return [] unless step

              step.approver_ids
            end

            # Methods that should be implemented by the including class
            def approval_action
              raise NotImplementedError, "#{self.class} must implement #approval_action"
            end

            def system_name
              raise NotImplementedError, "#{self.class} must implement #system_name"
            end

            # Define delegated methods with error handling
            %i[approve! reject! comment! cancel!].each do |method_name|
              define_method(:"approval_#{method_name}") do |*args, raise_error: false, **kwargs|
                return handle_missing_approval_request(method_name, *args, raise_error: raise_error, **kwargs) unless approval_request

                result = approval_request.public_send(method_name, *args, **kwargs)

                if raise_error && result.failure?
                  raise StandardError, result.message
                end

                result
              end
            end
          end

          # For backward compatibility, include InstanceMethods when the concern is included directly
          included do
            # Define the association
            has_one :approval_request, as: :approvable, dependent: :destroy, class_name: 'ApprovalRequest'

            # Approval status delegated to the approval request
            # Use a different method name to avoid conflicts with existing status methods
            delegate :status, to: :approval_request, prefix: :approval_workflow, allow_nil: true

            # Include the instance methods
            include InstanceMethods

            # Define scopes for finding approvable objects

            # Find all approvable objects that can be approved by the given user
            scope :pending_approval_for, ->(user_id) {
              includes(approval_request: :approval_steps)
                .where(approval_requests: { status: :pending })
                .where("approval_steps.approver_ids @> ?", [ user_id.to_s ].to_json)
                .distinct
            }

            # Find all approvable objects that have been approved in the approval workflow
            scope :approval_workflow_approved, -> {
              includes(:approval_request)
                .where(approval_requests: { status: :approved })
            }

            # Find all approvable objects that have been rejected in the approval workflow
            scope :approval_workflow_rejected, -> {
              includes(:approval_request)
                .where(approval_requests: { status: :rejected })
            }

            # Find all approvable objects that are pending approval in the approval workflow
            scope :approval_workflow_pending, -> {
              includes(:approval_request)
                .where(approval_requests: { status: :pending })
            }
          end
        end
      end
    end
  end
end
