#!/usr/bin/env ruby

# Simple test without Rails dependencies
require 'active_model'
require 'active_support'
require 'active_support/core_ext'
require 'securerandom'

# Load our ActiveStruct files directly
require_relative 'lib/athar/commons/active_struct/associations'
require_relative 'lib/athar/commons/active_struct/base'
require_relative 'lib/athar/commons/active_struct/collection'

puts "Testing ActiveStruct Optional ID Implementation"
puts "=" * 50

# Test 1: Default behavior (with ID)
class TestModelWithId < Athar::Commons::ActiveStruct::Base
  attribute :name, :string
end

model_with_id = TestModelWithId.new(name: 'Test')
puts 'Test 1 - Default behavior (with ID):'
puts "  ✓ has_id_attribute: #{model_with_id.class.has_id_attribute}"
puts "  ✓ auto_generate_id: #{model_with_id.class.auto_generate_id}"
puts "  ✓ id_enabled?: #{model_with_id.class.id_enabled?}"
puts "  ✓ responds to id?: #{model_with_id.respond_to?(:id)}"
puts "  ✓ id present?: #{model_with_id.id.present?}"
puts "  ✓ persisted?: #{model_with_id.persisted?}"
puts

# Test 2: Model without ID
class TestModelWithoutId < Athar::Commons::ActiveStruct::Base
  without_id
  attribute :name, :string
end

model_without_id = TestModelWithoutId.new(name: 'Test')
puts 'Test 2 - Model without ID:'
puts "  ✓ has_id_attribute: #{model_without_id.class.has_id_attribute}"
puts "  ✓ auto_generate_id: #{model_without_id.class.auto_generate_id}"
puts "  ✓ id_enabled?: #{model_without_id.class.id_enabled?}"
puts "  ✓ responds to id?: #{model_without_id.respond_to?(:id)}"
puts "  ✓ persisted?: #{model_without_id.persisted?}"
puts

# Test 3: Model with ID but no auto-generation
class TestModelManualId < Athar::Commons::ActiveStruct::Base
  with_id(auto_generate: false)
  attribute :name, :string
end

model_manual_id = TestModelManualId.new(name: 'Test')
puts 'Test 3 - Model with ID but no auto-generation:'
puts "  ✓ has_id_attribute: #{model_manual_id.class.has_id_attribute}"
puts "  ✓ auto_generate_id: #{model_manual_id.class.auto_generate_id}"
puts "  ✓ id_enabled?: #{model_manual_id.class.id_enabled?}"
puts "  ✓ responds to id?: #{model_manual_id.respond_to?(:id)}"
puts "  ✓ id present?: #{model_manual_id.id.present?}"
puts "  ✓ persisted?: #{model_manual_id.persisted?}"
puts

# Test 4: Model with manual ID set
model_manual_id_set = TestModelManualId.new(id: 'manual-123', name: 'Test')
puts 'Test 4 - Model with manual ID set:'
puts "  ✓ id: #{model_manual_id_set.id}"
puts "  ✓ persisted?: #{model_manual_id_set.persisted?}"
puts

# Test 5: Collection functionality
class TestCollection
  include Athar::Commons::ActiveStruct::Collection
  
  def item_class
    TestModelWithId
  end
end

collection = TestCollection.new
collection << TestModelWithId.new(name: 'Item 1')
collection << TestModelWithId.new(name: 'Item 2')

puts 'Test 5 - Collection with ID-enabled models:'
puts "  ✓ Collection size: #{collection.size}"
puts "  ✓ IDs count: #{collection.ids.length}"
puts "  ✓ Find by ID works: #{!collection.find(collection.ids.first).nil?}"
puts

# Test 6: Collection with non-ID models
class TestCollectionNoId
  include Athar::Commons::ActiveStruct::Collection
  
  def item_class
    TestModelWithoutId
  end
end

collection_no_id = TestCollectionNoId.new
collection_no_id << TestModelWithoutId.new(name: 'Item 1')
collection_no_id << TestModelWithoutId.new(name: 'Item 2')

puts 'Test 6 - Collection with non-ID models:'
puts "  ✓ Collection size: #{collection_no_id.size}"
puts "  ✓ IDs (should be empty): #{collection_no_id.ids}"
puts "  ✓ Find returns nil: #{collection_no_id.find('any-id').nil?}"
puts

puts "=" * 50
puts "✅ All tests completed successfully!"
puts "✅ Optional ID implementation is working correctly!"
