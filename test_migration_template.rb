#!/usr/bin/env ruby
require 'erb'
require 'ostruct'
require 'active_support'
require 'active_support/core_ext/string'

# Test cases
test_cases = [
  {
    name: "Project-based with foreign keys",
    options: {
      user_table: "users",
      project_based: true,
      project_table: "projects",
      use_foreign_keys: true
    }
  },
  {
    name: "Non-project-based without foreign keys",
    options: {
      user_table: "users",
      project_based: false,
      project_table: "projects",
      use_foreign_keys: false
    }
  }
]

# Template to test
template_path = "lib/generators/athar/commons/approval/templates/create_approval_requests.rb.tt"
template_content = File.read(template_path)

# Test each case
test_cases.each do |test_case|
  puts "\nTest case: #{test_case[:name]}"
  puts "-" * 40
  
  # Create a binding with the options
  options = OpenStruct.new(test_case[:options])
  binding_obj = OpenStruct.new(options: options).instance_eval { binding }
  
  # Render the template
  erb = ERB.new(template_content, trim_mode: '-')
  result = erb.result(binding_obj)
  
  # Check for any index creation on approvable
  if result.include?("index") && result.include?("approvable")
    puts "WARNING: Found potential explicit index creation on approvable columns"
    puts "This might cause conflicts with the automatic index created by t.references"
  else
    puts "No explicit index creation on approvable columns found - Good!"
  end
  
  # Print the result
  puts "\nOutput:"
  puts result
end
