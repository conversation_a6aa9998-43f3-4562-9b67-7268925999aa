require "active_support"
require "active_support/core_ext"
require "ostruct"

# Only require Rails components if we need them for Rails-specific specs
# This prevents Rails 8.0 compatibility issues for simple specs
begin
  require "rails"
  require "active_record"
  require "action_controller"

  # Prevent Rails from loading ActionView::Template::Handlers::ERB::ENCODING_FLAG
  # which is not available in Rails 8.0
  begin
    require "action_view"
  rescue NameError => e
    # Ignore the ENCODING_FLAG constant error in Rails 8.0
    raise e unless e.message.include?('ENCODING_FLAG')
  end

  require "rspec/rails"
rescue LoadError, NameError
  # If Rails components fail to load, we'll use basic RSpec setup
  require "rspec"
end

require "factory_bot_rails"
require "athar-commons"

RSpec.configure do |config|
  # Enable flags like --only-failures and --next-failure
  config.example_status_persistence_file_path = ".rspec_status"

  # Disable RSpec exposing methods globally on `Module` and `main`
  config.disable_monkey_patching!

  config.expect_with :rspec do |c|
    c.syntax = :expect
  end

  config.include FactoryBot::Syntax::Methods
end
