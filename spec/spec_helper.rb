require "active_support"
require "active_support/core_ext"
require "rails"
require "active_record"
require "action_controller"
require "rspec/rails"
require "factory_bot_rails"
require "athar-commons"

RSpec.configure do |config|
  # Enable flags like --only-failures and --next-failure
  config.example_status_persistence_file_path = ".rspec_status"

  # Disable RSpec exposing methods globally on `Module` and `main`
  config.disable_monkey_patching!

  config.expect_with :rspec do |c|
    c.syntax = :expect
  end

  config.include FactoryBot::Syntax::Methods
end
