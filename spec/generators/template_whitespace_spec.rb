require 'erb'
require 'ostruct'
require 'active_support/core_ext/string'

RSpec.describe "Template Whitespace" do
  # Define test cases with different options
  let(:test_cases) do
    [
      {
        name: "Project-based with foreign keys",
        options: {
          user_table: "users",
          project_based: true,
          project_table: "projects",
          use_foreign_keys: true
        }
      },
      {
        name: "Project-based without foreign keys",
        options: {
          user_table: "users",
          project_based: true,
          project_table: "projects",
          use_foreign_keys: false
        }
      },
      {
        name: "Non-project-based with foreign keys",
        options: {
          user_table: "users",
          project_based: false,
          project_table: "projects",
          use_foreign_keys: true
        }
      },
      {
        name: "Non-project-based without foreign keys",
        options: {
          user_table: "users",
          project_based: false,
          project_table: "projects",
          use_foreign_keys: false
        }
      }
    ]
  end

  # Test templates for consecutive empty lines
  describe "template generation" do
    let(:templates) do
      [
        "approval_request.rb.tt",
        "create_approval_requests.rb.tt",
        "approval_request_serializer.rb.tt"
      ]
    end

    it "doesn't generate templates with consecutive empty lines" do
      templates.each do |template_name|
        template_path = File.join(
          File.dirname(__FILE__),
          "../../lib/generators/athar/commons/approval/templates",
          template_name
        )
        
        expect(File.exist?(template_path)).to be true
        
        template_content = File.read(template_path)
        
        test_cases.each do |test_case|
          # Create a binding with the options
          options = OpenStruct.new(test_case[:options])
          
          # Create a context with helper methods
          context = Object.new
          context.instance_variable_set(:@options, options)
          
          # Define helper methods
          context.define_singleton_method(:options) { @options }
          
          # Add String extensions for the template
          String.include(ActiveSupport::CoreExtensions::String::Inflections) if String.respond_to?(:include)
          
          binding_obj = context.instance_eval { binding }
          
          # Render the template
          erb = ERB.new(template_content, trim_mode: '-')
          result = erb.result(binding_obj)
          
          # Check for consecutive empty lines
          consecutive_empty_lines = result.scan(/\n\s*\n\s*\n/)
          expect(consecutive_empty_lines).to be_empty, 
            "Template #{template_name} with options #{test_case[:name]} has consecutive empty lines"
          
          # Check for empty lines at the beginning
          expect(result).not_to start_with("\n"), 
            "Template #{template_name} with options #{test_case[:name]} starts with an empty line"
          
          # Check for multiple empty lines at the end
          expect(result).not_to end_with("\n\n"), 
            "Template #{template_name} with options #{test_case[:name]} ends with multiple empty lines"
        end
      end
    end
  end
end
