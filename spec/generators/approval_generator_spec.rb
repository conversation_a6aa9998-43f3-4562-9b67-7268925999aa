require 'spec_helper'
require 'generators/athar/commons/approval/approval_generator'

RSpec.describe Athar::Commons::Generators::ApprovalGenerator, type: :generator do
  # Define test cases with different options
  let(:test_cases) do
    [
      {
        name: "Project-based with foreign keys",
        options: ["--user_table=users", "--project_based=true", "--project_table=projects", "--use_foreign_keys=true"]
      },
      {
        name: "Project-based without foreign keys",
        options: ["--user_table=users", "--project_based=true", "--project_table=projects", "--use_foreign_keys=false"]
      },
      {
        name: "Non-project-based with foreign keys",
        options: ["--user_table=users", "--project_based=false", "--use_foreign_keys=true"]
      },
      {
        name: "Non-project-based without foreign keys",
        options: ["--user_table=users", "--project_based=false", "--use_foreign_keys=false"]
      }
    ]
  end

  # Test templates for consecutive empty lines
  describe "template generation" do
    let(:templates) do
      [
        "approval_request.rb.tt",
        "create_approval_requests.rb.tt",
        "approval_request_serializer.rb.tt"
      ]
    end

    it "doesn't generate templates with consecutive empty lines" do
      templates.each do |template_name|
        template_path = File.join(
          File.dirname(Athar::Commons::Generators::ApprovalGenerator.instance_method(:create_approval_models).source_location.first),
          "templates",
          template_name
        )
        
        template_content = File.read(template_path)
        
        test_cases.each do |test_case|
          # Parse options
          options = {}
          test_case[:options].each do |option|
            if option =~ /--(\w+)=(.+)/
              key = $1.to_sym
              value = $2
              # Convert string boolean values to actual booleans
              value = (value == "true") if value == "true" || value == "false"
              options[key] = value
            end
          end
          
          # Create a binding with the options
          binding_obj = OpenStruct.new(options: OpenStruct.new(options)).instance_eval { binding }
          
          # Render the template
          erb = ERB.new(template_content, trim_mode: '-')
          result = erb.result(binding_obj)
          
          # Check for consecutive empty lines
          consecutive_empty_lines = result.scan(/\n\s*\n\s*\n/)
          expect(consecutive_empty_lines).to be_empty, 
            "Template #{template_name} with options #{options.inspect} has consecutive empty lines"
          
          # Check for empty lines at the beginning or end
          expect(result).not_to start_with("\n"), 
            "Template #{template_name} with options #{options.inspect} starts with an empty line"
          expect(result).not_to end_with("\n\n"), 
            "Template #{template_name} with options #{options.inspect} ends with multiple empty lines"
        end
      end
    end
  end
end
