RSpec.describe "Template Files" do
  let(:templates) do
    [
      "lib/generators/athar/commons/approval/templates/approval_request.rb.tt",
      "lib/generators/athar/commons/approval/templates/create_approval_requests.rb.tt",
      "lib/generators/athar/commons/approval/templates/create_approval_steps.rb.tt",
      "lib/generators/athar/commons/approval/templates/create_approval_actions.rb.tt",
      "lib/generators/athar/commons/approval/templates/approval_request_serializer.rb.tt"
    ]
  end

  it "doesn't have lines with only whitespace in template files" do
    templates.each do |template_path|
      full_path = File.join(File.dirname(__FILE__), "../../#{template_path}")
      expect(File.exist?(full_path)).to be true
      
      content = File.read(full_path)
      whitespace_only_lines = content.lines.select { |line| line.match(/^\s+$/) }
      
      if !whitespace_only_lines.empty?
        puts "Template: #{template_path}"
        whitespace_only_lines.each_with_index do |line, i|
          puts "Line #{i+1}: #{line.inspect}"
        end
      end
      
      expect(whitespace_only_lines).to be_empty, 
        "Template #{template_path} has lines with only whitespace"
    end
  end
end
