require 'erb'
require 'ostruct'
require 'active_support'
require 'active_support/core_ext/string'

RSpec.describe "Approval Generator Templates" do
  let(:test_cases) do
    [
      {
        name: "Project-based with foreign keys",
        options: {
          user_table: "users",
          project_based: true,
          project_table: "projects",
          use_foreign_keys: true
        }
      },
      {
        name: "Non-project-based without foreign keys",
        options: {
          user_table: "users",
          project_based: false,
          project_table: "projects",
          use_foreign_keys: false
        }
      }
    ]
  end

  let(:templates) do
    [
      {
        name: "ApprovalRequest Model",
        path: "lib/generators/athar/commons/approval/templates/approval_request.rb.tt"
      },
      {
        name: "ApprovalRequests Migration",
        path: "lib/generators/athar/commons/approval/templates/create_approval_requests.rb.tt"
      },
      {
        name: "ApprovalSteps Migration",
        path: "lib/generators/athar/commons/approval/templates/create_approval_steps.rb.tt"
      },
      {
        name: "ApprovalActions Migration",
        path: "lib/generators/athar/commons/approval/templates/create_approval_actions.rb.tt"
      },
      {
        name: "ApprovalRequest Serializer",
        path: "lib/generators/athar/commons/approval/templates/approval_request_serializer.rb.tt"
      }
    ]
  end

  describe "template generation" do
    it "doesn't generate templates with lines containing only whitespace" do
      templates.each do |template|
        template_path = File.join(File.dirname(__FILE__), "../../#{template[:path]}")
        expect(File.exist?(template_path)).to be true
        
        template_content = File.read(template_path)
        
        test_cases.each do |test_case|
          # Create a binding with the options
          options = OpenStruct.new(test_case[:options])
          binding_obj = OpenStruct.new(options: options).instance_eval { binding }
          
          # Render the template
          erb = ERB.new(template_content, trim_mode: '-')
          result = erb.result(binding_obj)
          
          # Check for lines with only whitespace (spaces or tabs)
          whitespace_only_lines = result.lines.select { |line| line.match(/^\s+$/) }
          
          # Print the whitespace lines for debugging
          if !whitespace_only_lines.empty?
            puts "Template: #{template[:name]}, Options: #{test_case[:name]}"
            whitespace_only_lines.each_with_index do |line, i|
              puts "Line #{i+1}: #{line.inspect}"
            end
          end
          
          expect(whitespace_only_lines).to be_empty, 
            "Template #{template[:name]} with options #{test_case[:name]} has lines with only whitespace"
        end
      end
    end

    it "doesn't create duplicate indexes in migration templates" do
      migration_template = templates.find { |t| t[:name] == "ApprovalRequests Migration" }
      template_path = File.join(File.dirname(__FILE__), "../../#{migration_template[:path]}")
      template_content = File.read(template_path)
      
      test_cases.each do |test_case|
        # Create a binding with the options
        options = OpenStruct.new(test_case[:options])
        binding_obj = OpenStruct.new(options: options).instance_eval { binding }
        
        # Render the template
        erb = ERB.new(template_content, trim_mode: '-')
        result = erb.result(binding_obj)
        
        # Check for explicit index creation on approvable columns
        # We should only have the t.references line, not an explicit index
        references_count = result.scan(/t\.references\s+:approvable/).size
        explicit_index_count = result.scan(/t\.index\s+\[\s*:approvable_type\s*,\s*:approvable_id\s*\]/).size
        
        expect(references_count).to eq(1)
        expect(explicit_index_count).to eq(0)
      end
    end
  end
end
