# frozen_string_literal: true

require 'rails_helper'

# Configure Apipie for testing
::Apipie.configure do |config|
  config.validate = false
  config.app_info = 'Test API'
  config.api_base_url = '/api'
  config.doc_base_url = '/apidoc'
  config.default_version = '1.0'
  config.validate_value = false
  config.validate_presence = false
  config.validate_key = false
  config.process_params = false
  config.app_name = 'Test App'
  config.copyright = ''
  config.markup = ::Apipie::Markup::RDoc.new
  config.namespaced_resources = false
  config.show_all_examples = true
  config.use_cache = false
end

# Test controller for parameter conversion (without A<PERSON>pie DSL to avoid complexity)
class ParameterTypeConverterTestController < ActionController::Base
  include Athar::Commons::Api::Concerns::ParameterTypeConverter

  before_action :convert_param_types, only: [:index]

  def index
    render json: params.to_unsafe_h
  end

  def create
    render json: params.to_unsafe_h
  end

  private

  # Mock the Apipie method definitions that the concern expects
  def self.apipie_method_definitions
    {
      'index' => {
        'params' => {
          'id' => { 'type' => :number },
          'page' => {
            'type' => Hash,
            'params' => {
              'number' => { 'type' => :number },
              'size' => { 'type' => :number }
            }
          },
          'filter' => {
            'type' => Hash,
            'params' => {
              'active' => { 'type' => :boolean },
              'ids' => { 'type' => Array },
              'nested' => {
                'type' => Hash,
                'params' => {
                  'field1' => { 'type' => :number },
                  'field2' => { 'type' => :boolean },
                  'deep' => {
                    'type' => Hash,
                    'params' => {
                      'very_deep' => { 'type' => :number }
                    }
                  }
                }
              }
            }
          }
        }
      },
      'create' => {
        'params' => {
          'user' => {
            'type' => Hash,
            'params' => {
              'name' => { 'type' => String },
              'age' => { 'type' => :number }
            }
          }
        }
      }
    }
  end
end

RSpec.describe Athar::Commons::Api::Concerns::ParameterTypeConverter, type: :request do
  # Set up routes for the test controller
  before do
    Rails.application.routes.draw do
      get 'test_index' => 'parameter_type_converter_test#index'
      post 'test_create' => 'parameter_type_converter_test#create'
    end
  end
  
  # Reset routes after tests
  after do
    Rails.application.reload_routes!
  end
  
  describe "parameter type conversion" do
    it "converts integer parameters" do
      get "/test_index", params: { 
        id: "123", 
        page: { number: "10", size: "20" }
      }
      
      # Parse the response
      json_response = JSON.parse(response.body)
      
      # Check that the parameters were converted
      expect(json_response['id']).to eq(123)
      expect(json_response['page']['number']).to eq(10)
      expect(json_response['page']['size']).to eq(20)
    end
    
    it "converts boolean parameters" do
      get "/test_index", params: { 
        filter: { active: "true" }
      }
      
      # Parse the response
      json_response = JSON.parse(response.body)
      
      # Check that the parameters were converted
      expect(json_response['filter']['active']).to eq(true)
    end
    
    it "converts array parameters" do
      get "/test_index", params: { 
        filter: { ids: "1,2,3" }
      }
      
      # Parse the response
      json_response = JSON.parse(response.body)
      
      # Check that the parameters were converted
      expect(json_response['filter']['ids']).to eq(['1', '2', '3'])
    end
    
    it "handles deeply nested parameters" do
      get "/test_index", params: { 
        filter: { 
          nested: {
            field1: "42",
            field2: "false",
            deep: {
              very_deep: "100"
            }
          }
        }
      }
      
      # Parse the response
      json_response = JSON.parse(response.body)
      
      # Check that the parameters were converted
      expect(json_response['filter']['nested']['field1']).to eq(42)
      expect(json_response['filter']['nested']['field2']).to eq(false)
      expect(json_response['filter']['nested']['deep']['very_deep']).to eq(100)
    end
    
    it "does not convert parameters for non-GET requests" do
      post "/test_create", params: { 
        user: { 
          name: "John", 
          age: "30" # This should remain a string in a POST request
        }
      }
      
      # Parse the response
      json_response = JSON.parse(response.body)
      
      # Check that the parameters were not converted
      expect(json_response['user']['age']).to eq("30")
    end
  end
end
