# frozen_string_literal: true

require 'rails_helper'
require_relative '../../../../app/controllers/concerns/athar/commons/api/concerns/parameter_type_converter'

# Configure Apipie for testing
::Apipie.configure do |config|
  config.validate = false
  config.app_info = 'Test API'
  config.api_base_url = '/api'
  config.doc_base_url = '/apidoc'
  config.default_version = '1.0'
  config.validate_value = false
  config.validate_presence = false
  config.validate_key = false
  config.process_params = false
  config.app_name = 'Test App'
  config.copyright = ''
  config.markup = ::Apipie::Markup::RDoc.new
  config.namespaced_resources = false
  config.show_all_examples = true
  config.use_cache = false
end

# Test controller with actual Apipie documentation
class ParameterTypeConverterTestController < ActionController::Base
  include Apipie::DSL::Controller
  include Athar::Commons::Api::Concerns::ParameterTypeConverter

  before_action :convert_param_types, only: [:index]
  
  api :GET, '/test_index', 'Test endpoint for parameter conversion'
  param :id, :number, desc: 'User ID'
  param :page, Hash, desc: 'Pagination parameters' do
    param :number, :number, desc: 'Page number'
    param :size, :number, desc: 'Page size'
  end
  param :filter, Hash, desc: 'Filter parameters' do
    param :active, :boolean, desc: 'Active status'
    param :ids, Array, desc: 'Array of IDs'
    param :nested, Hash, desc: 'Nested parameters' do
      param :field1, :number, desc: 'Numeric field'
      param :field2, :boolean, desc: 'Boolean field'
      param :deep, Hash, desc: 'Deeply nested parameters' do
        param :very_deep, :number, desc: 'Deeply nested numeric field'
      end
    end
  end
  def index
    render json: params.to_unsafe_h
  end
  
  api :POST, '/test_create', 'Test endpoint for non-GET requests'
  param :user, Hash, desc: 'User data' do
    param :name, String, desc: 'User name'
    param :age, :number, desc: 'User age'
  end
  def create
    render json: params.to_unsafe_h
  end
end

RSpec.describe Athar::Commons::Api::Concerns::ParameterTypeConverter, type: :request do
  # Set up routes for the test controller
  before do
    Rails.application.routes.draw do
      get 'test_index' => 'parameter_type_converter_test#index'
      post 'test_create' => 'parameter_type_converter_test#create'
    end
  end
  
  # Reset routes after tests
  after do
    Rails.application.reload_routes!
  end
  
  describe "parameter type conversion" do
    it "converts integer parameters" do
      get "/test_index", params: { 
        id: "123", 
        page: { number: "10", size: "20" }
      }
      
      # Parse the response
      json_response = JSON.parse(response.body)
      
      # Check that the parameters were converted
      expect(json_response['id']).to eq(123)
      expect(json_response['page']['number']).to eq(10)
      expect(json_response['page']['size']).to eq(20)
    end
    
    it "converts boolean parameters" do
      get "/test_index", params: { 
        filter: { active: "true" }
      }
      
      # Parse the response
      json_response = JSON.parse(response.body)
      
      # Check that the parameters were converted
      expect(json_response['filter']['active']).to eq(true)
    end
    
    it "converts array parameters" do
      get "/test_index", params: { 
        filter: { ids: "1,2,3" }
      }
      
      # Parse the response
      json_response = JSON.parse(response.body)
      
      # Check that the parameters were converted
      expect(json_response['filter']['ids']).to eq(['1', '2', '3'])
    end
    
    it "handles deeply nested parameters" do
      get "/test_index", params: { 
        filter: { 
          nested: {
            field1: "42",
            field2: "false",
            deep: {
              very_deep: "100"
            }
          }
        }
      }
      
      # Parse the response
      json_response = JSON.parse(response.body)
      
      # Check that the parameters were converted
      expect(json_response['filter']['nested']['field1']).to eq(42)
      expect(json_response['filter']['nested']['field2']).to eq(false)
      expect(json_response['filter']['nested']['deep']['very_deep']).to eq(100)
    end
    
    it "does not convert parameters for non-GET requests" do
      post "/test_create", params: { 
        user: { 
          name: "John", 
          age: "30" # This should remain a string in a POST request
        }
      }
      
      # Parse the response
      json_response = JSON.parse(response.body)
      
      # Check that the parameters were not converted
      expect(json_response['user']['age']).to eq("30")
    end
  end
end
