# frozen_string_literal: true

# This file is copied to spec/ when you run 'rails generate rspec:install'
require 'spec_helper'

# Require Rails and related gems
require 'rails'
require 'active_record'
require 'action_controller'
require 'action_view'

# Prevent Rails from loading ActionView::Template::Handlers::ERB::ENCODING_FLAG
# which is not available in Rails 8.0
begin
  require 'action_view/template/handlers/erb'
rescue NameError => e
  # Ignore the ENCODING_FLAG constant error in Rails 8.0
  raise e unless e.message.include?('ENCODING_FLAG')
end

require 'rspec/rails'
require 'factory_bot_rails'

# Load the gem
require 'athar_commons'

# Configure Rails for testing
module TestApp
  class Application < Rails::Application
    config.load_defaults Rails::VERSION::STRING.to_f
    config.eager_load = false
    config.cache_classes = true
    config.consider_all_requests_local = true
    config.secret_key_base = 'test_secret_key_base'

    # Configure for API-only mode
    config.api_only = true

    # Configure SQLite database for testing
    config.active_record.database_url = 'sqlite3::memory:'

    # Disable unnecessary middleware for testing
    begin
      config.middleware.delete ActionDispatch::Cookies
      config.middleware.delete ActionDispatch::Session::CookieStore
      config.middleware.delete ActionDispatch::Flash
    rescue
      # Ignore errors if middleware doesn't exist
    end
  end
end

# Initialize the test Rails application
TestApp::Application.initialize! unless Rails.application

# Set up the database connection
ActiveRecord::Base.establish_connection(
  adapter: 'sqlite3',
  database: ':memory:'
)

# Manually trigger the engine's autoload configuration
if defined?(Athar::Commons::Engine)
  # Get the engine's root path
  engine_root = Athar::Commons::Engine.root

  # Add the engine's app paths to autoload_paths
  Rails.application.config.autoload_paths += [
    "#{engine_root}/app/controllers",
    "#{engine_root}/app/controllers/concerns",
    "#{engine_root}/app/models",
    "#{engine_root}/app/models/concerns",
    "#{engine_root}/app/services",
    "#{engine_root}/app/validators"
  ]

  # Trigger autoloader setup
  Rails.application.config.autoload_paths.uniq!
  Rails.autoloaders.main.setup
end

# Configure RSpec for Rails
RSpec.configure do |config|
  # Remove this line if you're not using ActiveRecord or ActiveRecord fixtures
  config.fixture_paths = ["#{::Rails.root}/spec/fixtures"]

  # If you're not using ActiveRecord, or you'd prefer not to run each of your
  # examples within a transaction, remove the following line or assign false
  # instead of true.
  config.use_transactional_fixtures = false

  # You can uncomment this line to turn off ActiveRecord support entirely.
  # config.use_active_record = false

  # RSpec Rails can automatically mix in different behaviours to your tests
  # based on their file location, for example enabling you to call `get` and
  # `post` in specs under `spec/controllers`.
  config.infer_spec_type_from_file_location!

  # Filter lines from Rails gems in backtraces.
  config.filter_rails_from_backtrace!

  # Include FactoryBot methods
  config.include FactoryBot::Syntax::Methods

  # Configure request specs
  config.include ActionDispatch::TestProcess::FixtureFile, type: :request
end
