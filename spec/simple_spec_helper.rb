require 'active_model'
require 'active_support'
require 'active_support/core_ext'
require 'securerandom'

# Load our ActiveStruct files directly
require_relative '../lib/athar/commons/active_struct/associations'
require_relative '../lib/athar/commons/active_struct/base'
require_relative '../lib/athar/commons/active_struct/collection'

RSpec.configure do |config|
  config.disable_monkey_patching!
  config.expect_with :rspec do |c|
    c.syntax = :expect
  end
end
