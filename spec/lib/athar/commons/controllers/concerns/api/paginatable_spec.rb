require "spec_helper"

RSpec.describe Athar::Commons::Api::Concerns::Paginatable do
  let(:controller_class) do
    Class.new(ActionController::API) do
      include Athar::Commons::Api::Concerns::Paginatable
      
      def params
        @params ||= {}
      end
    end
  end
  
  let(:controller) { controller_class.new }
  let(:pagy) { double("Pagy", page: 2, pages: 10, count: 100, vars: { items: 25 }) }
  let(:collection) { double("Collection") }
  let(:records) { double("Records") }
  
  describe "#paginate" do
    it "paginates the collection and returns records with metadata" do
      expect(controller).to receive(:pagy).with(collection, items: 25).and_return([pagy, records])
      expect(controller).to receive(:pagy_metadata).with(pagy).and_return({ pagination: { current_page: 2 } })
      
      result_records, result_meta = controller.paginate(collection)
      
      expect(result_records).to eq(records)
      expect(result_meta).to eq({ pagination: { current_page: 2 } })
    end
    
    it "uses page_size from params if provided" do
      controller.params[:page_size] = 50
      
      expect(controller).to receive(:pagy).with(collection, items: 50).and_return([pagy, records])
      expect(controller).to receive(:pagy_metadata).with(pagy).and_return({ pagination: { current_page: 2 } })
      
      controller.paginate(collection)
    end
  end
  
  describe "#pagy_metadata" do
    it "generates pagination metadata" do
      metadata = controller.pagy_metadata(pagy)
      
      expect(metadata).to eq({
        pagination: {
          current_page: 2,
          total_pages: 10,
          total_count: 100,
          per_page: 25
        }
      })
    end
  end
end
