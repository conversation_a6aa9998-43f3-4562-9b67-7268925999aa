require "spec_helper"
require_relative "../../../../app/models/concerns/athar/commons/models/concerns/ransackable"

RSpec.describe Athar::Commons::Models::Concerns::Ransackable do
  let(:model_class) do
    Class.new(ActiveRecord::Base) do
      self.table_name = "employees"
      include Athar::Commons::Models::Concerns::Ransackable
      
      enum :department, {
        admin: 0,
        hr: 1,
        finance: 2
      }
      
      enum :status, {
        active: 0,
        inactive: 1
      }
    end
  end

  describe ".ransackable_attributes" do
    it "returns all column names" do
      allow(model_class).to receive(:column_names).and_return(["id", "name", "department", "status"])
      expect(model_class.ransackable_attributes).to eq(["id", "name", "department", "status"])
    end
  end

  describe ".ransackable_associations" do
    it "returns all association names" do
      association = double("Association", name: :user)
      allow(model_class).to receive(:reflect_on_all_associations).and_return([association])
      expect(model_class.ransackable_associations).to eq(["user"])
    end
  end

  describe ".extract_enum_name_from_args" do
    context "when called with a hash" do
      it "extracts the enum name from the hash keys" do
        args = [{ department: { admin: 0, hr: 1 } }]
        expect(model_class.extract_enum_name_from_args(*args)).to eq("department")
      end
    end

    context "when called with a symbol and hash" do
      it "extracts the enum name from the symbol" do
        args = [:department, { admin: 0, hr: 1 }]
        expect(model_class.extract_enum_name_from_args(*args)).to eq("department")
      end
    end

    context "when called with a symbol, hash, and options" do
      it "extracts the enum name from the symbol" do
        args = [:status, { active: 0, inactive: 1 }, { prefix: true }]
        expect(model_class.extract_enum_name_from_args(*args)).to eq("status")
      end
    end
  end
  
  describe ".ransackable_enum" do
    it "creates ransacker for specific enum" do
      # This test is a placeholder for integration testing
      # In a real application, you would test this by:
      # 1. Creating test records with different enum values
      # 2. Testing filtering with string values for enum_eq
      # 3. Testing filtering with string values for enum_in
      #
      # Example:
      # hr_employee = create(:employee, department: :hr)
      # finance_employee = create(:employee, department: :finance)
      #
      # result = Employee.ransack(department_eq: 'hr').result
      # expect(result).to include(hr_employee)
      # expect(result).not_to include(finance_employee)
      #
      # result = Employee.ransack(department_in: ['hr', 'finance']).result
      # expect(result).to include(hr_employee)
      # expect(result).to include(finance_employee)
      
      # For now, we'll just verify the method exists
      expect(model_class).to respond_to(:ransackable_enum)
    end
  end
end
