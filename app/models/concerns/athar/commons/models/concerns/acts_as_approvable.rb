# frozen_string_literal: true

module Athar
  module Commons
    module Models
      module Concerns
        # This concern provides the acts_as_approvable method that can be used by any model
        # to make it "approvable". It's a generic concern that provides the basic functionality
        # needed for a model to work with the approval system.
        #
        # Usage:
        #   class MyModel < ApplicationRecord
        #     include Athar::Commons::Models::Concerns::ActsAsApprovable
        #     acts_as_approvable
        #   end
        module ActsAsApprovable
          extend ActiveSupport::Concern

          module ClassMethods
            # This method is called by models that want to be "approvable"
            # It adds the necessary associations and methods to the model
            # @param options [Hash] Options for customizing the approvable behavior
            # @option options [Symbol, Proc] :on_status_change Callback for status changes
            def acts_as_approvable(options = {})
              # Default callback is a method name
              callback = options[:on_status_change] || :on_approval_status_change

              # Store the callback in a class attribute
              class_attribute :approval_status_change_callback
              self.approval_status_change_callback = callback

              # Define the association
              has_one :approval_request, as: :approvable, dependent: :destroy, class_name: 'ApprovalRequest'

              # Approval status delegated to the approval request
              # Use a different method name to avoid conflicts with existing status methods
              delegate :status, to: :approval_request, prefix: :approval_workflow, allow_nil: true

              # Include the instance methods from the Approvable concern
              include Athar::Commons::Models::Concerns::Approvable::InstanceMethods

              # Define scopes for finding approvable objects

              # Find all approvable objects that can be approved by the given user
              scope :pending_approval_for, ->(user_id) {
                includes(approval_request: :approval_steps)
                  .where(approval_requests: { status: :pending })
                  .where("approval_steps.approver_ids @> ?", [user_id.to_s].to_json)
                  .distinct
              }

              # Find all approvable objects that have been approved in the approval workflow
              scope :approval_workflow_approved, -> {
                includes(:approval_request)
                  .where(approval_requests: { status: :approved })
              }

              # Find all approvable objects that have been rejected in the approval workflow
              scope :approval_workflow_rejected, -> {
                includes(:approval_request)
                  .where(approval_requests: { status: :rejected })
              }

              # Find all approvable objects that are pending approval in the approval workflow
              scope :approval_workflow_pending, -> {
                includes(:approval_request)
                  .where(approval_requests: { status: :pending })
              }
            end
          end
        end
      end
    end
  end
end
