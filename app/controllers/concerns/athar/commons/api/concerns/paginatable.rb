require "pagy"
require "pagy/extras/metadata"
require "pagy/extras/jsonapi"
require "pagy/extras/limit"

module Athar
  module Commons
    module Api
      module Concerns
        module Paginatable
          extend ActiveSupport::Concern

          included do
            include Pagy::Backend
          end

          def paginate(collection)
            options = { jsonapi: true }
            options[:limit] = params.dig(:page, Pagy::DEFAULT[:limit_param]) if params.dig(:page, Pagy::DEFAULT[:limit_param])
            pagy, records = pagy(collection, **options)
            [ records, { pagination: pagy_metadata(pagy) } ]
          end

        end
      end
    end
  end
end
