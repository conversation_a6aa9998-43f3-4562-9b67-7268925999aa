require "jsonapi/serializer" # For serializing models to JSON:API format
require "jsonapi" # For JSON:API filtering, sorting, etc.

module Athar
  module Commons
    module Api
      module Concerns
        # Provides methods for serializing resources and errors in JSON:API format
        #
        # This concern adds methods to controllers for rendering resources and errors
        # according to the JSON:API specification (https://jsonapi.org).
        #
        # @example
        #   class ApiController < ApplicationController
        #     include Athar::Commons::Api::Concerns::Serializable
        #
        #     def show
        #       @resource = YourModel.find(params[:id])
        #       serialize_response(@resource)
        #     end
        #   end
        module Serializable
          extend ActiveSupport::Concern

          # Renders a resource or collection with the appropriate serializer
          #
          # @param resource [Object, Array] The resource or collection to serialize
          # @param options [Hash] Options for serialization
          # @option options [Hash] :meta Additional metadata to include in the response
          # @option options [Array] :include Related resources to include
          # @option options [Symbol, Integer] :status HTTP status code (default: :ok)
          # @option options [Hash] :options Additional options to pass to the serializer
          # @return [void]
          def serialize_response(resource, options = {})
            serialization_options = prepare_serialization_options(resource, options)

            render jsonapi: resource,
                   meta: serialization_options[:meta],
                   include: serialization_options[:include],
                   status: serialization_options[:status],
                   **serialization_options[:serializer_options]
          end

          # Renders error responses in JSON:API format
          #
          # @param errors [Hash, ActiveModel::Errors] The errors to serialize
          # @param status [Symbol, Integer] HTTP status code (default: :unprocessable_entity)
          # @return [void]
          def serialize_errors(errors, status = :unprocessable_entity)
            render jsonapi_errors: errors, status: status
          end

          private

          # Prepares options for serialization
          #
          # This method can be overridden in controllers to customize serialization options.
          #
          # @param resource [Object, Array] The resource being serialized
          # @param options [Hash] The options passed to serialize_response
          # @return [Hash] The prepared options
          def prepare_serialization_options(resource, options = {})
            meta = options[:meta] || {}

            # Get includes from params and options, and merge them
            param_includes = parse_include_param(params[:include])
            option_includes = options[:include] || []
            includes = (param_includes + option_includes).uniq

            serializer_options = options[:options] || {}

            {
              meta: meta,
              include: includes,
              status: options[:status] || :ok,
              serializer_options: serializer_options
            }
          end

          # Parse the include parameter, supporting both string and array formats
          #
          # @param include_param [String, Array, nil] The include parameter
          # @return [Array] An array of includes
          def parse_include_param(include_param)
            case include_param
            when String then include_param.split(",")
            when Array then include_param
            else []
            end
          end

          # Handles abort exceptions and serializes errors
          #
          # @param exception [UncaughtThrowError] The exception to handle
          # @return [void]
          # @raise [UncaughtThrowError] If the exception is not an :abort throw
          def handle_abort_exception(exception)
            if exception.tag == :abort
              # If the record has errors, serialize them
              if instance_variable_defined?('@' + controller_name.singularize) &&
                 (record = instance_variable_get('@' + controller_name.singularize)) &&
                 record.respond_to?(:errors) &&
                 !record.errors.empty?
                serialize_errors(record.errors)
              else
                # Generic error if no specific errors are available
                serialize_errors({ detail: "Operation aborted" })
              end
            else
              # Re-raise if it's not an :abort throw
              raise exception
            end
          end

          included do
            # Handle UncaughtThrowError exceptions (e.g., from throw(:abort) in callbacks)
            rescue_from UncaughtThrowError, with: :handle_abort_exception
          end
        end
      end
    end
  end
end
