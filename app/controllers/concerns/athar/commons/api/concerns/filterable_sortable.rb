require "ransack"

module <PERSON><PERSON>
  module Commons
    module Api
      module Concerns
        module FilterableSortable
            extend ActiveSupport::Concern

            included do
              include JSONAPI::Filtering
            end

            # Apply filters to a collection based on params
            #
            # Supports standard Ransack filters as well as full-text search via filter[search].
            # The full-text search is implemented using PostgreSQL's full-text search capabilities
            # and will search across all searchable fields in the model.
            #
            # @param collection [ActiveRecord::Relation] The collection to filter
            # @return [ActiveRecord::Relation] The filtered collection
            def apply_filters(collection)
              # Check if we have a search query parameter
              search_query = params.dig(:filter, :search)

              # Apply full-text search if available and requested
              if search_query.present?
                resource_class = collection.model
                resource_name = resource_class.name.underscore.pluralize

                # Try different search method patterns in order of preference
                search_method = nil

                # 1. Try model-specific search method (e.g., search_employees for Employee)
                specific_search_method = "search_#{resource_name}"
                if resource_class.respond_to?(specific_search_method)
                  search_method = specific_search_method
                end

                # 2. Try generic search method
                if search_method.nil? && resource_class.respond_to?(:search)
                  search_method = :search
                end

                # 3. Try full_text_search method
                if search_method.nil? && resource_class.respond_to?(:full_text_search)
                  search_method = :full_text_search
                end

                # Apply the search method if found
                if search_method
                  collection = resource_class.send(search_method, search_query)
                end
              end

              # Get allowed filters for the model
              resource_class = collection.model
              allowed_filters = resource_class.respond_to?(:ransackable_attributes) ?
                                  resource_class.ransackable_attributes :
                                  resource_class.column_names

              # Check if we have any standard filters to apply
              filter_params = params[:filter]&.dup || {}
              filter_params.delete(:search) # Remove the search parameter as it's not a standard Ransack filter

              # Apply standard filters using jsonapi_filter
              jsonapi_filter(collection, allowed_filters) do |filtered|
                filtered_sorted_result = filtered.result
                yield filtered_sorted_result if block_given?
                filtered_sorted_result
              end
            end
        end
      end
    end
  end
end
