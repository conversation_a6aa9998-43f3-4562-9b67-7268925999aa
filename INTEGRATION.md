# Integration Guide

This guide provides step-by-step instructions for integrating the Athar Commons gem into your microservice.

## 1. Add the gem to your Gemfile

```ruby
gem 'athar_commons', git: 'https://github.com/athar/athar-commons.git'
```

Run `bundle install` to install the gem.

## 2. Run the install generator

```bash
$ rails generate athar:commons:install
```

This will create all the necessary configuration files automatically.

Alternatively, you can manually configure each component as described below.

## 3. Configure the gem manually (optional)

### Apipie Configuration

Create or update `config/initializers/apipie.rb`:

```ruby
::Apipie.configure do |config|
  config.app_name                = "Your Microservice Name"
  config.api_base_url            = "/api"
  config.doc_base_url            = "/apipie"
  config.default_version         = "1.0"
  config.validate                = false
  config.translate               = false
  config.default_locale          = nil
  config.app_info                = "API Documentation for Your Microservice"
  config.reload_controllers      = Rails.env.development?
  config.api_controllers_matcher = "#{Rails.root}/app/controllers/**/*.rb"
end
```

### Pagy Configuration

Create or update `config/initializers/pagy.rb`:

```ruby
require 'pagy/extras/metadata'
require 'pagy/extras/items'
require 'pagy/extras/jsonapi'

# The gem automatically configures these defaults:
# Pagy::DEFAULT[:items] = 20
# Pagy::DEFAULT[:max_items] = 100
# Pagy::DEFAULT[:metadata] = %i[count page limit from to]
```

### JSONAPI Configuration

Create or update `config/initializers/jsonapi.rb`:

```ruby
JSONAPI::Rails.configure do |config|
  # Set a default serializer for ActiveRecord::Base
  config.jsonapi_class = "JSONAPI::Serializer"

  # Set a default error serializer
  config.jsonapi_errors_class = "JSONAPI::Rails::SerializableError"

  # Set a default error renderer
  config.jsonapi_errors_renderer = "JSONAPI::Rails::Renderer::Errors"
end
```

## 3. Create a Base API Controller

Create `app/controllers/api/base_controller.rb`:

```ruby
module Api
  class BaseController < Athar::Commons::Controllers::Api::BaseController
    # Add your authentication logic here
    # before_action :authenticate_api_user!

    # Add any other common controller functionality
  end
end
```

## 4. Update Your Models

Update your models to include the Ransackable concern:

```ruby
class YourModel < ApplicationRecord
  include Athar::Commons::Models::Concerns::Ransackable

  # Your model code...
end
```

## 5. Create Resource Controllers

Create your resource controllers inheriting from the base controller:

```ruby
module Api
  class YourResourcesController < ApplicationController
    include Athar::Commons::Controllers::Concerns::Api::JsonapiDocs

    before_action :authenticate_user!
    before_action :set_resource, only: %i[show update destroy]

    # API documentation
    api! "Lists all resources"
    header "Authorization", "Bearer token", required: true
    param_group :pagination_params
    param_group :filter_params
    param_group :sort_params
    param_group :include_params
    param_group :fields_params
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Returns a list of all resources.
      Supports filtering, sorting, and pagination.
    HTML
    )
    returns code: 200, desc: "List of resources"

    def index
      collection = YourModel.all

      # Apply filters and sorting
      apply_filters(collection) do |filtered_and_sorted|
        # Apply pagination and get metadata
        records, meta = paginate(filtered_and_sorted)

        # Render the response with serializer
        serialize_response(records, meta: meta)
      end
    end

    # Other controller actions...

    private

    def set_resource
      @resource = YourModel.find(params[:id])
    end

    def resource_params
      params.require(:resource).permit(:attribute1, :attribute2)
    end
  end
end
```

## 6. Create Serializers

Create serializers for your models:

```ruby
class YourModelSerializer
  include JSONAPI::Serializer

  attributes :attribute1, :attribute2

  # Add any custom attributes or relationships
  attribute :custom_attribute do |resource|
    resource.calculate_something
  end

  belongs_to :related_model, if: Proc.new { |record, params| params[:include]&.include?('related_model') }
end
```

## 7. Test Your API

Your API should now support:
- JSON:API compliant responses
- Filtering using Ransack predicates
- Sorting
- Pagination
- API documentation via Apipie

Example API requests:

```
# List with pagination
GET /api/your_resources?page[number]=2&page[size]=10

# List with filtering
GET /api/your_resources?filter[attribute1_eq]=value&filter[attribute2_cont]=partial

# List with sorting
GET /api/your_resources?sort=attribute1,-attribute2

# List with includes
GET /api/your_resources?include=related_model

# List with sparse fieldsets
GET /api/your_resources?fields[your_model]=id,attribute1
```

## 8. View API Documentation

Visit `/apipie` in your browser to view the generated API documentation.
